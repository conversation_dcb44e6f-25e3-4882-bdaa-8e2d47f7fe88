<template>
  <div class="java-chapter10">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <div class="chapter-info">
            <h1 class="chapter-title">第十章：Clojure</h1>
            <p class="chapter-subtitle">A different view of programming</p>
            <p class="chapter-description">
              探索函数式编程的精髓：从Lisp血统到现代并发，掌握这门"代码即数据"的强大语言
            </p>
          </div>
          <div class="progress-indicator">
            <div class="progress-circle">
              <svg class="progress-ring" width="120" height="120">
                <circle
                  class="progress-ring-circle"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                />
                <circle
                  class="progress-ring-circle progress-ring-circle-fill"
                  stroke="white"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                  :stroke-dasharray="`${2 * Math.PI * 52}`"
                  :stroke-dashoffset="`${2 * Math.PI * 52 * (1 - progress / 100)}`"
                />
              </svg>
              <div class="progress-text">{{ Math.round(progress) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: Clojure 简介与核心理念 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="Clojure 简介与核心理念 (Introduction &amp; Philosophy)"
                :concept-data="clojureIntroductionData"
                @interaction="handleInteraction"
              >
                <div class="clojure-introduction-showcase">
                  <h3>🎯 Clojure：一种不同的编程视角</h3>
                  <p class="intro-text">
                    Clojure 是一门运行在 JVM 上的、动态类型的、函数式编程语言。它继承自古老而强大的
                    Lisp 家族， 但又融合了现代的并发和数据处理思想。如果你之前只熟悉 Java 或 Kotlin
                    这样的命令式、面向对象的语言， 那么 Clojure 将为你打开一扇通往全新世界的大门。
                  </p>

                  <div class="core-pillars">
                    <h4>🏗️ 两大核心基石</h4>
                    <div class="pillars-grid">
                      <div class="pillar-card">
                        <div class="card-icon">🔒</div>
                        <h5>不可变性 (Immutability)</h5>
                        <p>
                          默认情况下，Clojure 中创建的任何数据结构（如列表、映射）都是不可变的。
                          你不能修改它，只能创建它的一个包含新值的"修改后"的副本。
                        </p>
                        <div class="analogy">
                          <strong>类比：</strong>就像 Git 的版本控制。你每次
                          commit，都是在创建一个新的、 完整的历史快照，但 Git
                          内部会通过共享未改变的文件来节省空间。
                        </div>
                      </div>
                      <div class="pillar-card">
                        <div class="card-icon">💬</div>
                        <h5>REPL 驱动开发</h5>
                        <p>
                          REPL (Read-Eval-Print Loop) 是一个交互式编程环境，它是 Clojure
                          开发流程的中心。 开发者不断地在 REPL
                          中发送小段代码、获得即时反馈、并以这种"对话"的方式逐步构建和演进整个应用程序。
                        </p>
                        <div class="analogy">
                          <strong>类比：</strong
                          >就像一个顶级的模拟飞行驾驶舱。你可以在里面尝试各种飞行动作，
                          仪表盘（REPL）会立刻给你反馈。
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="immutability-demo">
                    <h4>🔄 不可变性实例演示</h4>
                    <div class="code-example">
                      <pre><code>(def user-v1 {:name "Alice" :age 30})  ; 定义一个不可变的map
(def user-v2 (assoc user-v1 :age 31)) ; 创建一个新map，age变为31

; user-v1 仍然是 {:name "Alice" :age 30}，没有被修改
; user-v2 是 {:name "Alice" :age 31}</code></pre>
                    </div>
                    <div class="explanation">
                      <p>
                        <strong>关键理解：</strong>Clojure
                        的世界里，数据一旦被创造出来，就永远不会改变，
                        像照片一样，只能复制，不能涂改。
                      </p>
                    </div>
                  </div>

                  <div class="clojure-mindmap">
                    <h4>🧠 Clojure 核心概念思维导图</h4>
                    <div class="mindmap-container">
                      <div class="mindmap-center">
                        <div class="center-node">Clojure</div>
                      </div>
                      <div class="mindmap-grid">
                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">🔒</div>
                            <h6>不可变性</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">持久化数据结构</span>
                            <span class="concept-item">结构共享</span>
                            <span class="concept-item">并发安全</span>
                          </div>
                        </div>

                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">💬</div>
                            <h6>REPL驱动</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">交互式开发</span>
                            <span class="concept-item">即时反馈</span>
                            <span class="concept-item">探索式编程</span>
                          </div>
                        </div>

                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">⚡</div>
                            <h6>函数式编程</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">高阶函数</span>
                            <span class="concept-item">代码即数据</span>
                            <span class="concept-item">组合性</span>
                          </div>
                        </div>

                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">🤝</div>
                            <h6>JVM互操作</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">Java库调用</span>
                            <span class="concept-item">生态系统</span>
                            <span class="concept-item">渐进迁移</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 语法与核心数据结构 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="语法与核心数据结构 (Syntax &amp; Core Data Structures)"
                :concept-data="syntaxDataStructuresData"
                @interaction="handleInteraction"
              >
                <div class="syntax-showcase">
                  <h3>📝 S-表达式：极致统一的语法</h3>
                  <p class="intro-text">
                    Clojure
                    的语法规则异常简单：所有东西都是括号包起来的列表，列表的第一个元素是"要做的事"（函数），
                    后面是"做事用的材料"（参数）。这被称为前缀表示法（Prefix Notation）。
                  </p>

                  <div class="syntax-demo">
                    <h4>🎯 语法基础：前缀表示法</h4>
                    <div class="syntax-comparison">
                      <div class="traditional">
                        <h6>传统中缀表示法</h6>
                        <pre><code>2 + 3 * 4
(a > b) && (c < d)</code></pre>
                      </div>
                      <div class="clojure">
                        <h6>Clojure 前缀表示法</h6>
                        <pre><code>(+ 2 (* 3 4))
(and (> a b) (< c d))</code></pre>
                      </div>
                    </div>
                    <div class="syntax-benefits">
                      <span class="benefit">🎯 无歧义性</span>
                      <span class="benefit">🔧 易于解析</span>
                      <span class="benefit">✨ 代码即数据</span>
                    </div>
                  </div>

                  <div class="data-structures-grid">
                    <h4>🗂️ 四大核心数据结构</h4>
                    <div class="structures-showcase">
                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">()</span>
                          <h5>List 列表</h5>
                        </div>
                        <div class="structure-content">
                          <p>不可变的链表，适合在头部增删</p>
                          <div class="code-example">
                            <pre><code>(def my-list '(1 2 3))
(first my-list)  ; 取第一个元素，O(1)
(rest my-list)   ; 取除第一个外的其余元素</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">🚀 头部访问：O(1)</span>
                            <span class="perf-item">⚠️ 随机访问：O(n)</span>
                          </div>
                        </div>
                      </div>

                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">[]</span>
                          <h5>Vector 向量</h5>
                        </div>
                        <div class="structure-content">
                          <p>不可变的"数组"，支持快速随机访问</p>
                          <div class="code-example">
                            <pre><code>(def my-vector [1 2 3])
(nth my-vector 2)  ; 取第3个元素，近似O(1)
(conj my-vector 4) ; 在尾部添加元素</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">🚀 随机访问：近似O(1)</span>
                            <span class="perf-item">🚀 尾部操作：近似O(1)</span>
                          </div>
                        </div>
                      </div>

                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">{}</span>
                          <h5>Map 映射</h5>
                        </div>
                        <div class="structure-content">
                          <p>不可变的哈希映射，键值对存储</p>
                          <div class="code-example">
                            <pre><code>(def my-map {:name "Alice" :age 30})
(:name my-map)     ; 用关键字取值
(my-map :name)     ; Map也可以当函数用</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">🚀 查找：近似O(1)</span>
                            <span class="perf-item">🚀 更新：近似O(1)</span>
                          </div>
                        </div>
                      </div>

                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">:</span>
                          <h5>Keyword 关键字</h5>
                        </div>
                        <div class="structure-content">
                          <p>特殊的标识符，通常用作 Map 的 key</p>
                          <div class="code-example">
                            <pre><code>:name :age :email
; 关键字可以作为函数使用
(:name {:name "Bob" :age 25})  ; => "Bob"</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">⚡ 查找速度比字符串快</span>
                            <span class="perf-item">🔄 自带缓存机制</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 函数式编程的精髓 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="函数式编程的精髓 (Essence of Functional Programming)"
                :concept-data="functionalProgrammingData"
                @interaction="handleInteraction"
              >
                <div class="functional-programming-showcase">
                  <h3>💡 代码即数据 (Code is Data)</h3>
                  <p class="intro-text">
                    在 Clojure
                    中，代码和数据使用相同的结构——S-表达式。这意味着程序可以像操作普通数据一样操作代码本身，
                    这是宏系统和元编程能力的基础。
                  </p>

                  <div class="code-as-data-demo">
                    <h4>🔄 代码即数据的体现</h4>
                    <div class="demo-grid">
                      <div class="demo-item">
                        <h6>数据结构</h6>
                        <div class="code-example">
                          <pre><code>'(+ 1 2 3)  ; 这是一个包含符号和数字的列表</code></pre>
                        </div>
                      </div>
                      <div class="demo-item">
                        <h6>可执行代码</h6>
                        <div class="code-example">
                          <pre><code>(+ 1 2 3)   ; 这是一个函数调用，返回 6</code></pre>
                        </div>
                      </div>
                    </div>
                    <div class="insight">
                      <p>
                        <strong>核心洞察：</strong
                        >唯一的区别是是否被求值。代码的结构就是数据的结构，
                        这使得程序可以生成、修改和执行其他程序。
                      </p>
                    </div>
                  </div>

                  <div class="higher-order-functions">
                    <h4>🎯 高阶函数：函数的函数</h4>
                    <p>
                      在 Clojure
                      中，函数是"一等公民"，可以作为参数传递、作为返回值、存储在数据结构中。
                      这使得代码具有极高的组合性和重用性。
                    </p>

                    <div class="hof-examples">
                      <div class="example-card">
                        <h6>map：转换每个元素</h6>
                        <div class="code-example">
                          <pre><code>(map inc [1 2 3 4])        ; => (2 3 4 5)
(map str ["a" "b" "c"])    ; => ("a" "b" "c")
(map #(* % %) [1 2 3 4])   ; => (1 4 9 16)</code></pre>
                        </div>
                      </div>

                      <div class="example-card">
                        <h6>filter：筛选符合条件的元素</h6>
                        <div class="code-example">
                          <pre><code>(filter even? [1 2 3 4 5 6])     ; => (2 4 6)
(filter #(> % 10) [5 15 8 20])  ; => (15 20)</code></pre>
                        </div>
                      </div>

                      <div class="example-card">
                        <h6>reduce：累积计算</h6>
                        <div class="code-example">
                          <pre><code>(reduce + [1 2 3 4])           ; => 10
(reduce conj [] [1 2 3])       ; => [1 2 3]
(reduce max [3 1 4 1 5])       ; => 5</code></pre>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 实际项目踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题：误用 List 作为 reduce 的累积器导致性能问题</h4>
                        <p>
                          一个从 Java 转来的开发者，在用 Clojure
                          处理一个大型数据集时，习惯性地用一个 list 来收集处理结果， 代码类似
                          <code>(reduce conj '() data-source)</code
                          >。当数据集非常大时，他发现这个操作的性能随着数据量的增长急剧下降。
                        </p>
                        <div class="code-example">
                          <h6>❌ 问题代码</h6>
                          <pre><code>; 错误：使用 list 作为累积器
(reduce conj '() large-dataset)
; 结果顺序是反的，还需要 reverse
(reverse (reduce conj '() large-dataset))</code></pre>
                        </div>
                      </div>

                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          <code>conj</code> 函数对于不同的集合类型，其行为是不同的。对于 list，<code
                            >conj</code
                          >
                          是在头部添加元素， 这是一个 O(1) 的高效操作。但由于 reduce
                          是从左到右处理数据源的，每次 conj 之后，新元素都在列表的最前面，
                          最终生成的列表顺序与数据源的顺序是相反的。为了得到正确的顺序，开发者不得不在最后再调用一次
                          reverse， 而 reverse 操作需要完整遍历一次列表，对于大列表来说开销巨大。
                        </p>
                      </div>

                      <div class="solutions">
                        <h4>业界最佳实践：使用 Vector 作为累积器</h4>
                        <div class="solution-demo">
                          <div class="code-example">
                            <h6>✅ 正确做法</h6>
                            <pre><code>; 使用 vector 作为累积器
(reduce conj [] large-dataset)
; 顺序正确，性能高效，无需额外操作</code></pre>
                          </div>
                          <div class="performance-comparison">
                            <h6>性能对比</h6>
                            <div class="comparison-grid">
                              <div class="comparison-item">
                                <h7>List + reverse</h7>
                                <ul>
                                  <li>conj: O(1) × n = O(n)</li>
                                  <li>reverse: O(n)</li>
                                  <li>总计: O(2n)</li>
                                </ul>
                              </div>
                              <div class="comparison-item">
                                <h7>Vector</h7>
                                <ul>
                                  <li>conj: 近似O(1) × n = O(n)</li>
                                  <li>无需额外操作</li>
                                  <li>总计: O(n)</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="best-practice-note">
                          <strong>核心原则：</strong>当使用 reduce
                          构建一个新的集合时，几乎总是应该使用 vector [] 作为初始值，
                          除非你明确需要一个 LIFO (后进先出) 栈的行为。这是 Clojure
                          开发中一条非常重要的性能惯例。
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 序列抽象 (seq) -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="序列抽象 (seq) (The Sequence Abstraction)"
                :concept-data="sequenceAbstractionData"
                @interaction="handleInteraction"
              >
                <div class="sequence-abstraction-showcase">
                  <h3>🔗 统一的集合操作接口</h3>
                  <p class="intro-text">
                    序列抽象（seq）是 Clojure
                    最强大的抽象之一。它提供了一个统一的接口来操作所有类型的集合， 无论是
                    List、Vector、Map，还是字符串、Java 数组，甚至是文件流。
                  </p>

                  <div class="seq-universality">
                    <h4>🌐 万物皆可序列化</h4>
                    <div class="seq-examples">
                      <div class="seq-example">
                        <h6>集合类型</h6>
                        <div class="code-example">
                          <pre><code>(seq [1 2 3])        ; Vector
(seq '(1 2 3))       ; List
(seq {:a 1 :b 2})    ; Map => ([:a 1] [:b 2])
(seq #{1 2 3})       ; Set</code></pre>
                        </div>
                      </div>

                      <div class="seq-example">
                        <h6>字符串和数组</h6>
                        <div class="code-example">
                          <pre><code>(seq "hello")        ; => (\h \e \l \l \o)
(seq (int-array [1 2 3]))  ; Java 数组</code></pre>
                        </div>
                      </div>

                      <div class="seq-example">
                        <h6>无限序列</h6>
                        <div class="code-example">
                          <pre><code>(take 5 (range))     ; => (0 1 2 3 4)
(take 5 (repeat "x")) ; => ("x" "x" "x" "x" "x")
(take 5 (cycle [1 2])) ; => (1 2 1 2 1)</code></pre>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="lazy-evaluation">
                    <h4>⚡ 惰性求值：按需计算</h4>
                    <p>
                      Clojure 的序列是惰性的，这意味着元素只有在真正需要时才会被计算。
                      这使得我们可以处理无限大的数据集，而不会耗尽内存。
                    </p>

                    <div class="lazy-demo">
                      <div class="demo-card">
                        <h6>无限序列的威力</h6>
                        <div class="code-example">
                          <pre><code>; 定义一个无限的斐波那契数列
(defn fib-seq []
  (map first (iterate (fn [[a b]] [b (+ a b)]) [0 1])))

; 只取前10个，不会计算后面的
(take 10 (fib-seq))  ; => (0 1 1 2 3 5 8 13 21 34)</code></pre>
                        </div>
                      </div>

                      <div class="demo-card">
                        <h6>链式操作的优化</h6>
                        <div class="code-example">
                          <pre><code>; 这个链式操作是惰性的，只在最后的 doall 时才执行
(->> (range 1000000)
     (filter even?)
     (map #(* % %))
     (take 5)
     doall)  ; => (0 4 16 36 64)</code></pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 元编程利器：宏 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="元编程利器：宏 (Macros: Code that Writes Code)"
                :concept-data="macrosData"
                @interaction="handleInteraction"
              >
                <div class="macros-showcase">
                  <h3>🎭 宏：塑造语言的能力</h3>
                  <p class="intro-text">
                    宏是 Clojure 的终极武器，它让开发者拥有"塑造语言"的能力。
                    宏可以在编译时生成代码，创建新的语法结构，甚至实现领域特定语言（DSL）。
                  </p>

                  <div class="macro-concept">
                    <h4>🔧 宏的工作原理</h4>
                    <div class="macro-flow">
                      <div class="flow-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                          <h6>编写宏</h6>
                          <p>定义一个接收代码作为数据的函数</p>
                        </div>
                      </div>
                      <div class="flow-arrow">→</div>
                      <div class="flow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                          <h6>宏展开</h6>
                          <p>编译时将宏调用替换为生成的代码</p>
                        </div>
                      </div>
                      <div class="flow-arrow">→</div>
                      <div class="flow-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                          <h6>代码执行</h6>
                          <p>运行时执行展开后的代码</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="macro-example">
                    <h4>💡 简单宏示例</h4>
                    <div class="code-example">
                      <pre><code>; 定义一个简单的 when-not 宏
(defmacro when-not [condition & body]
  `(if (not ~condition)
     (do ~@body)))

; 使用宏
(when-not false
  (println "This will print")
  (println "So will this"))

; 宏展开后的代码
(if (not false)
  (do
    (println "This will print")
    (println "So will this")))</code></pre>
                    </div>
                  </div>

                  <div class="macro-power">
                    <h4>⚡ 宏的强大之处</h4>
                    <div class="power-grid">
                      <div class="power-card">
                        <div class="power-icon">🏗️</div>
                        <h6>创建新语法</h6>
                        <p>可以创建看起来像内置语言特性的新语法结构</p>
                      </div>
                      <div class="power-card">
                        <div class="power-icon">🎯</div>
                        <h6>编译时优化</h6>
                        <p>在编译时进行代码优化和转换</p>
                      </div>
                      <div class="power-card">
                        <div class="power-icon">🔧</div>
                        <h6>DSL 构建</h6>
                        <p>构建领域特定语言，提高表达力</p>
                      </div>
                      <div class="power-card">
                        <div class="power-icon">⚡</div>
                        <h6>零运行时开销</h6>
                        <p>宏在编译时展开，运行时无额外开销</p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 6: 与 Java 的共生 -->
            <section id="topic-5" class="topic-section" ref="topic5">
              <ExpandableSection
                title="与 Java 的共生 (Symbiosis with Java)"
                :concept-data="javaInteropData"
                @interaction="handleInteraction"
              >
                <div class="java-interop-showcase">
                  <h3>🤝 无缝互操作的设计</h3>
                  <p class="intro-text">
                    Clojure 被设计为与 Java 完全兼容，可以直接调用 Java 类和方法， 使用 Java
                    库，甚至可以被 Java 代码调用。这种设计让 Clojure 能够充分利用庞大的 Java
                    生态系统。
                  </p>

                  <div class="interop-examples">
                    <h4>🔗 Java 互操作示例</h4>
                    <div class="interop-grid">
                      <div class="interop-card">
                        <h6>调用 Java 方法</h6>
                        <div class="code-example">
                          <pre><code>; 创建 Java 对象
(def date (java.util.Date.))

; 调用实例方法
(.toString date)
(.getTime date)

; 调用静态方法
(Math/abs -42)
(System/currentTimeMillis)</code></pre>
                        </div>
                      </div>

                      <div class="interop-card">
                        <h6>使用 Java 集合</h6>
                        <div class="code-example">
                          <pre><code>; 创建 ArrayList
(def list (java.util.ArrayList.))

; 添加元素
(.add list "hello")
(.add list "world")

; 转换为 Clojure 序列
(seq list)  ; => ("hello" "world")</code></pre>
                        </div>
                      </div>

                      <div class="interop-card">
                        <h6>实现 Java 接口</h6>
                        <div class="code-example">
                          <pre><code>; 实现 Runnable 接口
(def my-runnable
  (reify Runnable
    (run [this]
      (println "Running in thread"))))

; 创建并启动线程
(def thread (Thread. my-runnable))
(.start thread)</code></pre>
                        </div>
                      </div>

                      <div class="interop-card">
                        <h6>类型提示优化</h6>
                        <div class="code-example">
                          <pre><code>; 使用类型提示避免反射
(defn fast-string-length [^String s]
  (.length s))

; 数组操作
(defn sum-array [^doubles arr]
  (aget arr 0))</code></pre>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="ecosystem-benefits">
                    <h4>🌟 生态系统优势</h4>
                    <div class="benefits-grid">
                      <div class="benefit-card">
                        <div class="benefit-icon">📚</div>
                        <h6>丰富的库生态</h6>
                        <p>可以直接使用所有 Java 库，包括 Spring、Hibernate 等</p>
                      </div>
                      <div class="benefit-card">
                        <div class="benefit-icon">🚀</div>
                        <h6>成熟的 JVM</h6>
                        <p>享受 JVM 的性能优化、垃圾回收和监控工具</p>
                      </div>
                      <div class="benefit-card">
                        <div class="benefit-icon">🔧</div>
                        <h6>企业级工具</h6>
                        <p>可以使用 Maven、Gradle 等构建工具和 IDE 支持</p>
                      </div>
                      <div class="benefit-card">
                        <div class="benefit-icon">👥</div>
                        <h6>团队协作</h6>
                        <p>Java 和 Clojure 代码可以在同一项目中共存</p>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 实际项目踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题：未能转变开发心智模型导致效率低下</h4>
                        <p>
                          一个习惯了 Java "编写-编译-运行"模式的团队，在刚开始使用 Clojure
                          时，感到非常不适应。 他们仍然试图在 IDE 中写完一个庞大的 .clj
                          文件，然后通过 lein run 等命令来运行它。 这完全丧失了 REPL
                          的优势，并且当代码出错时，由于缺乏 REPL 的即时反馈，调试周期变得很长。
                        </p>
                      </div>

                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          未能转变开发的心智模型。他们把 Clojure 当成了"带括号的 Java"来写，
                          沿用了旧的工作流，而没有拥抱 REPL 驱动开发这种全新的、更高效的范式。
                        </p>
                      </div>

                      <div class="solutions">
                        <h4>解决方案：构建交互式开发环境</h4>
                        <div class="solution-steps">
                          <div class="step">
                            <h6>1. 选择合适的工具</h6>
                            <p>
                              使用对 Clojure 支持良好的编辑器（VS Code with Calva, Emacs with CIDER,
                              或 IntelliJ with Cursive）
                            </p>
                          </div>
                          <div class="step">
                            <h6>2. 改变工作流</h6>
                            <ul>
                              <li>自底向上构建：从最小的函数开始</li>
                              <li>持续交互：写一个函数，立即在 REPL 中测试</li>
                              <li>数据在手：在 REPL 中加载真实数据进行验证</li>
                              <li>注释驱动：使用 (comment ...) 块存放调试代码</li>
                            </ul>
                          </div>
                        </div>
                        <div class="best-practice-note">
                          <strong>核心原则：</strong>REPL 驱动开发是所有专业 Clojure 程序员的日常。
                          他们不是在"写程序"，而是在"与运行中的系统对话"。
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <BackToTopButton />

    <!-- 浮动章节菜单 -->
    <FloatingChapterMenu :chapters="chapterList" current-chapter="10" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import BackToTopButton from '@/components/BackToTopButton.vue'
import FloatingChapterMenu from '@/components/FloatingChapterMenu.vue'

// 响应式数据
const currentTopic = ref(0)
const progress = ref(0)

// 章节列表
const chapterList = [
  { number: 1, title: 'Java基础', path: '/chapter1' },
  { number: 2, title: 'Java高级特性', path: '/chapter2' },
  { number: 3, title: 'Java 17新特性', path: '/chapter3' },
  { number: 4, title: 'Java模块系统', path: '/chapter4' },
  { number: 5, title: 'Java虚拟机', path: '/chapter5' },
  { number: 6, title: 'Java并发编程', path: '/chapter6' },
  { number: 7, title: 'Java性能优化', path: '/chapter7' },
  { number: 8, title: 'JVM替代语言', path: '/chapter8' },
  { number: 9, title: 'Kotlin', path: '/chapter9' },
  { number: 10, title: 'Clojure', path: '/chapter10' },
]

// 课程主题
const courseTopics = [
  {
    title: 'Clojure 简介与核心理念',
    description: '理解不可变性与REPL驱动开发',
  },
  {
    title: '语法与核心数据结构',
    description: 'S-表达式与四大数据结构',
  },
  {
    title: '函数式编程的精髓',
    description: '代码即数据与高阶函数',
  },
  {
    title: '序列抽象 (seq)',
    description: '统一的集合操作接口',
  },
  {
    title: '元编程利器：宏',
    description: '代码生成代码的能力',
  },
  {
    title: '与 Java 的共生',
    description: '无缝互操作与生态利用',
  },
]

// 概念数据
const clojureIntroductionData = {
  definition:
    'Clojure 是一门运行在 JVM 上的、动态类型的、函数式编程语言，以不可变性和 REPL 驱动开发为核心基石。',
  keyPoints: [
    '不可变性：默认情况下所有数据结构都是不可变的',
    'REPL 驱动开发：交互式编程环境是开发流程的中心',
    '持久化数据结构：通过结构共享实现高效的不可变性',
    '动态编译：REPL 中的代码被即时编译成 JVM 字节码',
  ],
  coreValue: '从根本上简化对复杂状态和并发的管理，通过不可变性彻底消除数据竞争问题。',
  commonMistakes: [
    '认为不可变意味着性能低下',
    '混淆值(value)、符号(symbol)和变量(var)的概念',
    '沿用传统的编写-编译-运行模式而不拥抱REPL',
  ],
}

const syntaxDataStructuresData = {
  definition: 'Clojure 使用 S-表达式（前缀表示法）作为基本语法，提供四种核心的不可变数据结构。',
  keyPoints: [
    'S-表达式：(函数名 参数1 参数2 ...) 的统一格式',
    'List ()：不可变链表，适合头部操作',
    'Vector []：不可变数组，支持快速随机访问',
    'Map {}：不可变哈希映射，键值对存储',
    'Keyword :：特殊标识符，常用作Map的key',
  ],
  coreValue: '极致统一的语法使得语言解析和代码生成都变得异常简单，为宏系统奠定基础。',
  commonMistakes: [
    '混淆List和Vector的性能特征',
    '忘记在数据列表前加单引号阻止求值',
    '不理解conj函数对不同数据结构的多态行为',
  ],
}

const functionalProgrammingData = {
  definition: 'Clojure 中代码和数据使用相同的结构，函数是一等公民，支持高阶函数和组合式编程。',
  keyPoints: [
    '代码即数据：程序可以像操作数据一样操作代码',
    '高阶函数：map、filter、reduce等核心函数',
    '函数组合：通过组合简单函数构建复杂逻辑',
    '不可变性：避免副作用，提高代码可预测性',
  ],
  coreValue: '通过函数式编程范式，实现更高的代码组合性、重用性和可维护性。',
  commonMistakes: [
    '误用List作为reduce的累积器',
    '不理解惰性求值的特性',
    '过度使用递归而不使用尾递归优化',
  ],
}

const sequenceAbstractionData = {
  definition: '序列抽象提供统一的接口来操作所有类型的集合，支持惰性求值和无限序列。',
  keyPoints: [
    '统一接口：所有集合类型都可以序列化',
    '惰性求值：元素按需计算，节省内存',
    '无限序列：可以处理理论上无限大的数据集',
    '链式操作：支持优雅的函数式编程风格',
  ],
  coreValue: '通过统一的序列抽象，简化集合操作，提供强大的数据处理能力。',
  commonMistakes: [
    '不理解惰性求值的延迟特性',
    '在需要立即求值的场景下忘记使用doall',
    '对无限序列使用count等会导致无限循环的操作',
  ],
}

const macrosData = {
  definition: '宏是在编译时生成代码的强大工具，让开发者能够扩展语言语法和创建DSL。',
  keyPoints: [
    '编译时代码生成：宏在编译时展开为实际代码',
    '语法扩展：可以创建新的语言结构',
    'DSL构建：构建领域特定语言',
    '零运行时开销：宏展开后无额外性能损失',
  ],
  coreValue: '提供元编程能力，让开发者能够根据需要塑造和扩展语言。',
  commonMistakes: ['过度使用宏而不是函数', '不理解宏的卫生性问题', '在宏中使用副作用操作'],
}

const javaInteropData = {
  definition: 'Clojure与Java的无缝互操作，可以直接调用Java类和方法，充分利用Java生态系统。',
  keyPoints: [
    'Java方法调用：直接调用Java实例和静态方法',
    'Java对象创建：创建和使用Java对象',
    '接口实现：通过reify实现Java接口',
    '类型提示：使用类型提示优化性能',
  ],
  coreValue: '充分利用Java生态系统，实现渐进式迁移和混合开发。',
  commonMistakes: [
    '不使用类型提示导致反射性能损失',
    '未能适应REPL驱动的开发模式',
    '过度依赖Java思维而不拥抱函数式编程',
  ],
}

// 方法
const scrollToTopic = (index: number) => {
  const element = document.getElementById(`topic-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    currentTopic.value = index
  }
}

const handleInteraction = (data: any) => {
  console.log('Interaction:', data)
}

const toggleNotes = () => {
  console.log('Toggle notes')
}

const showQuiz = () => {
  console.log('Show quiz')
}

const updateProgress = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
  progress.value = (scrollTop / scrollHeight) * 100

  // 更新当前主题
  const topics = document.querySelectorAll('.topic-section')
  topics.forEach((topic, index) => {
    const rect = topic.getBoundingClientRect()
    if (rect.top <= 100 && rect.bottom >= 100) {
      currentTopic.value = index
    }
  })
}

onMounted(() => {
  window.addEventListener('scroll', updateProgress)
  updateProgress()
})

onUnmounted(() => {
  window.removeEventListener('scroll', updateProgress)
})
</script>

<style scoped>
.java-chapter10 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0 2rem;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    repeat;
  opacity: 0.1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chapter-info h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chapter-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.chapter-description {
  font-size: 1.1rem;
  opacity: 0.8;
  max-width: 600px;
  line-height: 1.6;
}

.progress-indicator {
  position: relative;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-fill {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 600;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content-wrapper {
  background: white;
  border-radius: 20px 20px 0 0;
  margin-top: 2rem;
  min-height: calc(100vh - 200px);
  box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.1);
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  padding: 2rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: #f8fafc;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
}

.outline h3 {
  margin-bottom: 1rem;
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.outline-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.outline-item:hover {
  background: white;
  border-color: #667eea;
  transform: translateX(4px);
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.outline-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.outline-content p {
  font-size: 0.8rem;
  opacity: 0.7;
  line-height: 1.3;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  padding: 0.75rem;
  border: none;
  border-radius: 12px;
  background: #f1f5f9;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.tool-button:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.main-content {
  padding: 1rem 0;
  overflow-x: hidden;
  max-width: 100%;
}

.topic-section {
  margin-bottom: 3rem;
  scroll-margin-top: 100px;
  overflow-x: hidden;
  max-width: 100%;
}

/* Clojure 特定样式 */
.clojure-introduction-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
  margin-bottom: 2rem;
}

.core-pillars {
  margin: 2rem 0;
}

.pillars-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.pillar-card {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.pillar-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.pillar-card h5 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.pillar-card p {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.analogy {
  background: rgba(102, 126, 234, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.5;
}

.immutability-demo {
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
  border: 1px solid #e2e8f0;
}

.code-example {
  background: #1a202c;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  overflow-x: auto;
  max-width: 100%;
  word-wrap: break-word;
}

.code-example pre {
  margin: 0;
  color: #e2e8f0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

.explanation {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

/* 思维导图样式 */
.clojure-mindmap {
  margin: 3rem 0;
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.mindmap-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.mindmap-center {
  margin-bottom: 1rem;
}

.center-node {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  margin: 0 auto;
}

.mindmap-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  width: 100%;
  max-width: 800px;
}

.concept-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  text-align: center;
}

.concept-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
}

.concept-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.concept-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.concept-header h6 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.concept-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.concept-item {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 8px;
  padding: 0.5rem 0.8rem;
  font-size: 0.9rem;
  color: #4a5568;
  transition: all 0.3s ease;
}

.concept-item:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-1px);
}

.syntax-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.syntax-demo {
  margin: 2rem 0;
}

.syntax-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;
}

.traditional,
.clojure {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.traditional h6,
.clojure h6 {
  margin-bottom: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.syntax-benefits {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.benefit {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.data-structures-grid {
  margin: 3rem 0;
}

.structures-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.structure-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.structure-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.structure-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.structure-symbol {
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  font-family: 'Fira Code', monospace;
}

.structure-header h5 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
}

.structure-content p {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.performance {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.perf-item {
  font-size: 0.8rem;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  background: #f1f5f9;
  color: #475569;
}

/* 函数式编程样式 */
.functional-programming-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.code-as-data-demo {
  margin: 2rem 0;
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.demo-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;
}

.demo-item h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.insight {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.higher-order-functions {
  margin: 3rem 0;
}

.hof-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.example-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.example-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.example-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.problem-solution {
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.problem {
  margin-bottom: 2rem;
}

.problem h4 {
  color: #e53e3e;
  margin-bottom: 1rem;
}

.root-cause {
  margin-bottom: 2rem;
}

.root-cause h4 {
  color: #d69e2e;
  margin-bottom: 1rem;
}

.solutions h4 {
  color: #38a169;
  margin-bottom: 1rem;
}

.solution-demo {
  margin: 1.5rem 0;
}

.performance-comparison {
  margin: 1.5rem 0;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1rem;
}

.comparison-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.comparison-item h7 {
  font-weight: 600;
  color: #2d3748;
  display: block;
  margin-bottom: 0.5rem;
}

.comparison-item ul {
  margin: 0;
  padding-left: 1rem;
}

.comparison-item li {
  font-size: 0.9rem;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.best-practice-note {
  background: rgba(56, 161, 105, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #38a169;
  margin-top: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 序列抽象样式 */
.sequence-abstraction-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.seq-universality {
  margin: 2rem 0;
}

.seq-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.seq-example {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.seq-example h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.lazy-evaluation {
  margin: 3rem 0;
}

.lazy-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.demo-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.demo-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.demo-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

/* 宏相关样式 */
.macros-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.macro-concept {
  margin: 2rem 0;
}

.macro-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 150px;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 1rem;
}

.step-content h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

.flow-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

.macro-example {
  margin: 2rem 0;
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.macro-power {
  margin: 3rem 0;
}

.power-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.power-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: transform 0.3s ease;
}

.power-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.power-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.power-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.power-card p {
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Java 互操作样式 */
.java-interop-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.interop-examples {
  margin: 2rem 0;
}

.interop-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.interop-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.interop-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.interop-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.ecosystem-benefits {
  margin: 3rem 0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.benefit-card {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: transform 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.benefit-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.benefit-card p {
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

.solution-steps {
  margin: 1.5rem 0;
}

.step {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
}

.step h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step p {
  color: #4a5568;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.step ul {
  margin: 0.5rem 0 0 1rem;
}

.step li {
  color: #4a5568;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .outline-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .pillars-grid {
    grid-template-columns: 1fr;
  }

  .syntax-comparison {
    grid-template-columns: 1fr;
  }

  .demo-grid {
    grid-template-columns: 1fr;
  }

  .hof-examples {
    grid-template-columns: 1fr;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .lazy-demo {
    grid-template-columns: 1fr;
  }

  .macro-flow {
    flex-direction: column;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .power-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .interop-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .chapter-header {
    padding: 2rem 0 1rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .chapter-info h1 {
    font-size: 2.5rem;
  }

  .container {
    padding: 0 1rem;
  }

  .content-layout {
    padding: 1rem;
  }

  .structures-showcase {
    grid-template-columns: 1fr;
  }

  .seq-examples {
    grid-template-columns: 1fr;
  }

  .power-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .macro-flow {
    gap: 0.5rem;
  }

  .flow-step {
    min-width: 120px;
  }

  .step-number {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .mindmap-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .center-node {
    width: 80px;
    height: 80px;
    font-size: 1rem;
  }

  .concept-card {
    padding: 1rem;
  }

  .concept-icon {
    font-size: 1.5rem;
  }

  .concept-header h6 {
    font-size: 1rem;
  }

  .concept-item {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }
}
</style>
