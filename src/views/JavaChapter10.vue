<template>
  <div class="java-chapter10">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <div class="chapter-info">
            <h1 class="chapter-title">第十章：Clojure</h1>
            <p class="chapter-subtitle">A different view of programming</p>
            <p class="chapter-description">
              探索函数式编程的精髓：从Lisp血统到现代并发，掌握这门"代码即数据"的强大语言
            </p>
          </div>
          <div class="progress-indicator">
            <div class="progress-circle">
              <svg class="progress-ring" width="120" height="120">
                <circle
                  class="progress-ring-circle"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                />
                <circle
                  class="progress-ring-circle progress-ring-circle-fill"
                  stroke="white"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                  :stroke-dasharray="`${2 * Math.PI * 52}`"
                  :stroke-dashoffset="`${2 * Math.PI * 52 * (1 - progress / 100)}`"
                />
              </svg>
              <div class="progress-text">{{ Math.round(progress) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: Clojure 简介与核心理念 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="Clojure 简介与核心理念 (Introduction &amp; Philosophy)"
                :concept-data="clojureIntroductionData"
                @interaction="handleInteraction"
              >
                <div class="clojure-introduction-showcase">
                  <h3>🎯 Clojure：一种不同的编程视角</h3>
                  <p class="intro-text">
                    Clojure 是一门运行在 JVM 上的、动态类型的、函数式编程语言。它继承自古老而强大的
                    Lisp 家族， 但又融合了现代的并发和数据处理思想。如果你之前只熟悉 Java 或 Kotlin
                    这样的命令式、面向对象的语言， 那么 Clojure 将为你打开一扇通往全新世界的大门。
                  </p>

                  <div class="core-pillars">
                    <h4>🏗️ 两大核心基石</h4>
                    <div class="pillars-grid">
                      <div class="pillar-card">
                        <div class="card-icon">🔒</div>
                        <h5>不可变性 (Immutability)</h5>
                        <p>
                          默认情况下，Clojure 中创建的任何数据结构（如列表、映射）都是不可变的。
                          你不能修改它，只能创建它的一个包含新值的"修改后"的副本。
                        </p>
                        <div class="analogy">
                          <strong>类比：</strong>就像 Git 的版本控制。你每次
                          commit，都是在创建一个新的、 完整的历史快照，但 Git
                          内部会通过共享未改变的文件来节省空间。
                        </div>
                      </div>
                      <div class="pillar-card">
                        <div class="card-icon">💬</div>
                        <h5>REPL 驱动开发</h5>
                        <p>
                          REPL (Read-Eval-Print Loop) 是一个交互式编程环境，它是 Clojure
                          开发流程的中心。 开发者不断地在 REPL
                          中发送小段代码、获得即时反馈、并以这种"对话"的方式逐步构建和演进整个应用程序。
                        </p>
                        <div class="analogy">
                          <strong>类比：</strong
                          >就像一个顶级的模拟飞行驾驶舱。你可以在里面尝试各种飞行动作，
                          仪表盘（REPL）会立刻给你反馈。
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="immutability-demo">
                    <h4>🔄 不可变性实例演示</h4>
                    <div class="code-example">
                      <pre><code>(def user-v1 {:name "Alice" :age 30})  ; 定义一个不可变的map
(def user-v2 (assoc user-v1 :age 31)) ; 创建一个新map，age变为31

; user-v1 仍然是 {:name "Alice" :age 30}，没有被修改
; user-v2 是 {:name "Alice" :age 31}</code></pre>
                    </div>
                    <div class="explanation">
                      <p>
                        <strong>关键理解：</strong>Clojure
                        的世界里，数据一旦被创造出来，就永远不会改变，
                        像照片一样，只能复制，不能涂改。
                      </p>
                    </div>
                  </div>

                  <div class="clojure-mindmap">
                    <h4>🧠 Clojure 核心概念思维导图</h4>
                    <div class="mindmap-container">
                      <div class="mindmap-center">
                        <div class="center-node">Clojure</div>
                      </div>
                      <div class="mindmap-grid">
                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">🔒</div>
                            <h6>不可变性</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">持久化数据结构</span>
                            <span class="concept-item">结构共享</span>
                            <span class="concept-item">并发安全</span>
                          </div>
                        </div>

                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">💬</div>
                            <h6>REPL驱动</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">交互式开发</span>
                            <span class="concept-item">即时反馈</span>
                            <span class="concept-item">探索式编程</span>
                          </div>
                        </div>

                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">⚡</div>
                            <h6>函数式编程</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">高阶函数</span>
                            <span class="concept-item">代码即数据</span>
                            <span class="concept-item">组合性</span>
                          </div>
                        </div>

                        <div class="concept-card">
                          <div class="concept-header">
                            <div class="concept-icon">🤝</div>
                            <h6>JVM互操作</h6>
                          </div>
                          <div class="concept-items">
                            <span class="concept-item">Java库调用</span>
                            <span class="concept-item">生态系统</span>
                            <span class="concept-item">渐进迁移</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 语法与核心数据结构 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="语法与核心数据结构 (Syntax &amp; Core Data Structures)"
                :concept-data="syntaxDataStructuresData"
                @interaction="handleInteraction"
              >
                <div class="syntax-showcase">
                  <h3>📝 S-表达式：极致统一的语法</h3>
                  <p class="intro-text">
                    Clojure
                    的语法规则异常简单：所有东西都是括号包起来的列表，列表的第一个元素是"要做的事"（函数），
                    后面是"做事用的材料"（参数）。这被称为前缀表示法（Prefix Notation）。
                  </p>

                  <div class="syntax-demo">
                    <h4>🎯 语法基础：前缀表示法</h4>
                    <div class="syntax-comparison">
                      <div class="traditional">
                        <h6>传统中缀表示法</h6>
                        <pre><code>2 + 3 * 4
(a > b) && (c < d)</code></pre>
                      </div>
                      <div class="clojure">
                        <h6>Clojure 前缀表示法</h6>
                        <pre><code>(+ 2 (* 3 4))
(and (> a b) (< c d))</code></pre>
                      </div>
                    </div>
                    <div class="syntax-benefits">
                      <span class="benefit">🎯 无歧义性</span>
                      <span class="benefit">🔧 易于解析</span>
                      <span class="benefit">✨ 代码即数据</span>
                    </div>
                  </div>

                  <div class="data-structures-grid">
                    <h4>🗂️ 四大核心数据结构</h4>
                    <div class="structures-showcase">
                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">()</span>
                          <h5>List 列表</h5>
                        </div>
                        <div class="structure-content">
                          <p>不可变的链表，适合在头部增删</p>
                          <div class="code-example">
                            <pre><code>(def my-list '(1 2 3))
(first my-list)  ; 取第一个元素，O(1)
(rest my-list)   ; 取除第一个外的其余元素</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">🚀 头部访问：O(1)</span>
                            <span class="perf-item">⚠️ 随机访问：O(n)</span>
                          </div>
                        </div>
                      </div>

                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">[]</span>
                          <h5>Vector 向量</h5>
                        </div>
                        <div class="structure-content">
                          <p>不可变的"数组"，支持快速随机访问</p>
                          <div class="code-example">
                            <pre><code>(def my-vector [1 2 3])
(nth my-vector 2)  ; 取第3个元素，近似O(1)
(conj my-vector 4) ; 在尾部添加元素</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">🚀 随机访问：近似O(1)</span>
                            <span class="perf-item">🚀 尾部操作：近似O(1)</span>
                          </div>
                        </div>
                      </div>

                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">{}</span>
                          <h5>Map 映射</h5>
                        </div>
                        <div class="structure-content">
                          <p>不可变的哈希映射，键值对存储</p>
                          <div class="code-example">
                            <pre><code>(def my-map {:name "Alice" :age 30})
(:name my-map)     ; 用关键字取值
(my-map :name)     ; Map也可以当函数用</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">🚀 查找：近似O(1)</span>
                            <span class="perf-item">🚀 更新：近似O(1)</span>
                          </div>
                        </div>
                      </div>

                      <div class="structure-card">
                        <div class="structure-header">
                          <span class="structure-symbol">:</span>
                          <h5>Keyword 关键字</h5>
                        </div>
                        <div class="structure-content">
                          <p>特殊的标识符，通常用作 Map 的 key</p>
                          <div class="code-example">
                            <pre><code>:name :age :email
; 关键字可以作为函数使用
(:name {:name "Bob" :age 25})  ; => "Bob"</code></pre>
                          </div>
                          <div class="performance">
                            <span class="perf-item">⚡ 查找速度比字符串快</span>
                            <span class="perf-item">🔄 自带缓存机制</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 函数式编程的精髓 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="函数式编程的精髓 (Essence of Functional Programming)"
                :concept-data="functionalProgrammingData"
                @interaction="handleInteraction"
              >
                <div class="functional-programming-showcase">
                  <h3>💡 高阶函数与闭包：行为的抽象和组合</h3>
                  <p class="intro-text">
                    在 Clojure 中，函数式编程的核心思想被提升到了极致。主要体现在两个概念上：
                    <strong>高阶函数 (Higher-Order Functions)</strong
                    >：一个函数可以接受另一个函数作为参数，或者可以返回一个函数作为结果。
                    <strong>闭包 (Closures)</strong
                    >：一个函数"捕获"并"记住"了其创建时所在环境中的变量，即使它在别处被调用，也依然可以访问那些被捕获的变量。
                  </p>

                  <div class="concept-explanation">
                    <h4>🎯 人话版解释</h4>
                    <p class="human-explanation">
                      在 Clojure
                      的世界里，<strong>行为（函数）和数据（如数字、字符串）是平等的</strong>。你可以把一个"行为"像普通数据一样装在盒子里、传来传去。闭包则更神奇，它像一个"带了记忆的机器人"，你配置好它之后（比如告诉它基准利率是3%），无论你把它派到哪里，它都永远记得这个基准利率。
                    </p>
                  </div>

                  <div class="core-principles">
                    <h4>🏗️ 深度解读与"第一性原理"追问</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <h6>存在价值</h6>
                        <p>
                          为了实现<strong>行为的抽象和组合</strong>。这使得代码的模块化和复用性达到了一个全新的高度。你可以编写出非常通用的逻辑骨架（如
                          <code>map</code>, <code>filter</code>,
                          <code>reduce</code
                          >），然后通过传入不同的"行为"（函数）来填充细节，从而灵活地应对各种需求。
                        </p>
                      </div>
                      <div class="principle-card">
                        <h6>核心原理</h6>
                        <p>
                          Clojure 的函数在底层被编译成实现了 <code>clojure.lang.IFn</code> 接口的
                          JVM
                          对象。这使得函数可以像任何其他对象一样被传递和存储。闭包的实现，是在创建函数对象时，编译器会将该函数需要引用的外部局部变量的值（或引用）作为成员变量存入这个函数对象中。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="closure-demo">
                    <h4>🔧 闭包实例演示</h4>
                    <div class="demo-section">
                      <h6>具象案例：一个可定制的问候语生成器</h6>
                      <div class="code-example">
                        <pre><code>(defn greeting-generator [greeting-word]
  (fn [name] (str greeting-word ", " name "!"))) ; 返回一个函数(闭包)

(def hello (greeting-generator "Hello")) ; 创建一个"Hello"版问候器
(def hola (greeting-generator "Hola"))   ; 创建一个"Hola"版问候器

(hello "World") ;=> "Hello, World!"
(hola "Mundo")  ;=> "Hola, Mundo!"</code></pre>
                      </div>
                      <p class="explanation">
                        这里的 <code>greeting-generator</code> 是一个高阶函数，<code>hello</code> 和
                        <code>hola</code> 都是闭包。
                      </p>
                    </div>

                    <div class="analogy">
                      <h6>🎨 生动类比</h6>
                      <p>
                        闭包就像一个<strong>预设了特定调料的"调味瓶"</strong>。<code
                          >greeting-generator</code
                        >
                        是一个空的调味瓶。你调用
                        <code>(greeting-generator "Hello")</code
                        >，就相当于往瓶子里灌满了"酱油"，并贴上"hello"的标签。这个"hello"瓶以后倒出来的永远是酱油味。你再调用
                        <code>(greeting-generator "Hola")</code
                        >，就是另一个灌满了"醋"并贴着"hola"标签的瓶子。
                      </p>
                    </div>
                  </div>

                  <div class="comparison-section">
                    <h4>🔄 横向对比与关联</h4>
                    <div class="comparison-grid">
                      <div class="comparison-item">
                        <h6>关联性</h6>
                        <p>
                          高阶函数是所有后续序列操作（<code>map</code>,
                          <code>filter</code> 等）的基础。
                        </p>
                      </div>
                      <div class="comparison-item">
                        <h6>vs. Java 8+ Lambdas</h6>
                        <p>
                          Java 8 也引入了高阶函数和闭包的概念。但 Clojure
                          的函数式特性是与生俱来的，更加纯粹和彻底。Java
                          的闭包只能捕获"事实最终"(effectively final) 的变量，而 Clojure
                          的闭包可以捕获可变状态（通过 <code>atom</code> 等）。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="pitfalls-section">
                    <h4>⚠️ 常见理论误区与注意事项</h4>
                    <div class="pitfall-card">
                      <h6>误区</h6>
                      <p>
                        认为函数式编程就是不用循环，只用递归。这是表象。函数式编程的核心是避免共享的可变状态，以及将函数作为一等公民进行组合。
                      </p>
                    </div>
                    <div class="pitfall-card">
                      <h6>注意</h6>
                      <p>
                        Clojure
                        的动态类型意味着，编译器不会检查你传给高阶函数的那个"函数"的参数数量和类型是否匹配。这类错误只会在运行时才会暴露，这要求更完备的测试。
                      </p>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 项目实践踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题描述</h4>
                        <p>
                          一个团队在用 Java
                          实现一个复杂的数据校验框架时，创建了大量的接口和实现类。例如，<code>StringNonEmptyValidator</code>,
                          <code>IntegerRangeValidator</code>,
                          <code>DateFutureValidator</code>
                          等。每增加一种新的校验规则，就需要定义一个新的类，导致类爆炸和大量的模板代码。
                        </p>
                      </div>

                      <div class="root-cause">
                        <h4>问题根源分析</h4>
                        <p>
                          <strong>将"行为"固化在了"类型"之中</strong>。Java 的传统 OO
                          思维倾向于用一个完整的类来封装一种行为。但在这里，"校验"这个行为的核心逻辑其实非常简单，没有必要用一个类的重型结构来承载。
                        </p>
                      </div>

                      <div class="solutions">
                        <h4>业界主流解决方案与权衡</h4>
                        <div class="solution-demo">
                          <h6>方案：使用函数（闭包）来代表校验规则</h6>
                          <p>在 Clojure 中，你不需要定义任何类。</p>
                          <ol>
                            <li>定义一个校验器注册表（一个 Map）。</li>
                            <li>将每个校验规则定义为一个简单的函数（闭包），并注册到 Map 中。</li>
                            <li>
                              编写一个通用的
                              <code>validate</code> 函数，它接受数据和规则集，动态地应用规则。
                            </li>
                          </ol>
                          <div class="code-example">
                            <pre><code>(defn in-range? [min max] #(and (>= % min) (<= % max)))
(def validators {:age (in-range? 18 65), :name #(not-empty %)})

; 通用校验函数
(defn validate [data rules]
  (reduce-kv (fn [errors field validator]
               (if (validator (get data field))
                 errors
                 (conj errors (str "Invalid " field))))
             []
             rules))</code></pre>
                          </div>
                        </div>

                        <div class="tradeoffs">
                          <h6>利弊与权衡</h6>
                          <div class="tradeoff-grid">
                            <div class="pros">
                              <h7>优点</h7>
                              <ul>
                                <li>
                                  极度灵活和简洁。增加新的校验规则，只需要定义一个新的函数并加入到
                                  Map 中，完全不需要创建新文件或新类。
                                </li>
                                <li>代码量减少一个数量级。</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h7>缺点</h7>
                              <ul>
                                <li>
                                  对于习惯了 Java
                                  类型系统的开发者，这种完全依赖于运行时动态组合的方式可能感觉"不安全"。
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>

                        <div class="industry-choice">
                          <h6>业界选择</h6>
                          <p>
                            这种<strong>"数据驱动"</strong>和<strong>"行为参数化"</strong>的设计是函数式编程的精髓。在需要高度灵活性和可配置性的场景（如校验器、工作流、规则引擎），这种模式远比僵化的类继承体系优越。即使在现代
                            Java 中，人们也越来越多地使用
                            <code>Map&lt;String, Predicate&lt;T&gt;&gt;</code>
                            这样的结构来模拟这种行为，这正是受函数式思想的影响。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 序列抽象 (seq) -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="序列抽象 (seq) (The Sequence Abstraction)"
                :concept-data="sequenceAbstractionData"
                @interaction="handleInteraction"
              >
                <div class="sequence-abstraction-showcase">
                  <h3>🔗 万能数据传送带：序列抽象</h3>
                  <p class="intro-text">
                    序列（Sequence，简称 <code>seq</code>）是 Clojure
                    中最核心、最强大的统一抽象。它代表了对一个数据集合的<strong>逻辑视图</strong>，这个视图的基本操作只有三个：<code>first</code>（取第一个元素），<code>rest</code>（取除第一个元素外的剩余序列），以及
                    <code>cons</code>（在序列头部添加一个元素）。最关键的是，序列默认是<strong
                      >惰性的 (Lazy)</strong
                    >。
                  </p>

                  <div class="concept-explanation">
                    <h4>🎯 人话版解释</h4>
                    <p class="human-explanation">
                      <code>seq</code> 是 Clojure
                      的"万能数据传送带"。无论你的数据原来是装在盒子里（Vector）、串在绳上（List）、还是正在从水管里流出来（网络流），Clojure
                      都可以给它套上一个统一的
                      <code>seq</code>
                      传送带接口。这个传送带非常"懒"，只有当你明确要取下一个物品时（<code>first</code>），它才会转动一下，把那个物品送到你面前。
                    </p>
                  </div>

                  <div class="core-principles">
                    <h4>🏗️ 深度解读与"第一性原理"追问</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <h6>存在价值</h6>
                        <div class="value-points">
                          <p>
                            <strong>1. 统一性</strong>：提供一套统一的函数库（<code>map</code>,
                            <code>filter</code> 等）来操作所有不同类型的数据集合。
                          </p>
                          <p>
                            <strong>2. 高性能与低内存</strong
                            >：通过<strong>惰性求值</strong>，避免了在处理大型数据集合时创建不必要的中间集合，也使得处理近乎无限大的数据集（如日志文件流）成为可能。
                          </p>
                        </div>
                      </div>
                      <div class="principle-card">
                        <h6>核心原理</h6>
                        <p>
                          <code>seq</code> 接口的实现。任何可以被顺序遍历的东西，都可以实现
                          <code>seq</code> 接口。惰性序列在被请求 <code>first</code> 或
                          <code>rest</code>
                          元素时，才会进行实际的计算，并且会缓存计算结果，下次访问时直接返回。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="practical-example">
                    <h4>🔧 具象案例：处理一个巨大的日志文件</h4>
                    <div class="code-example">
                      <pre><code>(with-open [rdr (clojure.java.io/reader "huge.log")]
  (->> (line-seq rdr)         ; 1. 得到一个惰性行序列，文件并未全部读入内存
       (filter #(clojure.string/includes? % "ERROR")) ; 2. 得到一个新的惰性序列，也未执行
       (take 10)              ; 3. 得到最终的惰性序列，也未执行
       (doall)))               ; 4. doall强制求值，此时才开始真正读文件、过滤，直到找到10个为止</code></pre>
                    </div>

                    <div class="analogy">
                      <h6>🎨 生动类比</h6>
                      <p>惰性序列就像<strong>在YouTube上看一个很长的播放列表</strong>。</p>
                      <ul>
                        <li>
                          <strong><code>line-seq</code></strong
                          >:
                          你打开了这个播放列表的页面，看到了所有视频的标题，但此时没有一个视频被下载到你的电脑里。
                        </li>
                        <li>
                          <strong><code>filter</code></strong
                          >:
                          你在搜索框里输入了"猫咪"，播放列表过滤出了所有标题含"猫咪"的视频，这个过滤结果也只是一个列表，视频本身仍然没有下载。
                        </li>
                        <li>
                          <strong><code>take 10</code></strong
                          >: 你决定只看前10个结果。
                        </li>
                        <li>
                          <strong><code>doall</code> (或开始观看)</strong>:
                          当你点击第一个视频的播放按钮时，YouTube
                          才开始把这个视频的数据流传给你。你看完一个，它再加载下一个。它永远不会一次性把10个视频都下载下来。
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="comparison-section">
                    <h4>🔄 横向对比与关联</h4>
                    <div class="comparison-grid">
                      <div class="comparison-item">
                        <h6>关联性</h6>
                        <p>
                          它是 Clojure 函数式编程风格的载体，<code>map</code>, <code>filter</code>,
                          <code>reduce</code> 等核心函数都作用于 <code>seq</code>。
                        </p>
                      </div>
                      <div class="comparison-item">
                        <h6>vs. Java Iterator</h6>
                        <p>
                          <code>Iterator</code> 是有状态的、可变的，遍历一次后就耗尽了。<code
                            >seq</code
                          >
                          是不可变的，你可以将一个
                          <code>seq</code> 传递给多个不同的函数，它们都可以从头开始遍历。
                        </p>
                      </div>
                      <div class="comparison-item">
                        <h6>vs. Java Stream</h6>
                        <p>
                          <code>Stream</code> 是 Java 8 对
                          <code>seq</code> 思想的借鉴，两者在惰性求值、链式操作等方面非常相似。但
                          <code>seq</code> 的概念在 Clojure 中更底层、更普遍。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="pitfalls-section">
                    <h4>⚠️ 常见理论误区与注意事项</h4>
                    <div class="pitfall-card danger">
                      <h6>天坑</h6>
                      <p>
                        <strong>无意中实现 (Realize) 了一个无限的惰性序列</strong>。例如，<code
                          >(range)</code
                        >
                        会生成一个无限的整数序列。如果你直接在 REPL 中求值
                        <code>(range)</code>，REPL 会试图打印所有元素，导致无限输出直到内存耗尽。
                      </p>
                    </div>
                    <div class="pitfall-card">
                      <h6>注意</h6>
                      <p>
                        在需要确保惰性序列被完全求值（通常是为了其副作用，如打印、写文件）时，必须使用
                        <code>doall</code> 或 <code>dorun</code> 来强制执行。
                      </p>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 项目实践踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题描述</h4>
                        <p>
                          一个应用需要从数据库中查询出所有"白金会员"的用户ID，然后对每个ID，再分别调用一个远程RPC服务来获取其详细信息。开发者的代码如下：
                        </p>
                        <div class="code-example">
                          <pre><code>(let [user-ids (get-platinum-user-ids-from-db) ; 返回一个包含1000个ID的集合
      user-details (map #(rpc-get-user-details %) user-ids)] ; 映射RPC调用
  (process-details user-details))</code></pre>
                        </div>
                        <p>
                          这段代码的性能非常差，处理1000个用户需要很长时间，而且经常因为RPC超时而失败。
                        </p>
                      </div>

                      <div class="root-cause">
                        <h4>问题根源分析</h4>
                        <p>
                          <strong>串行执行了I/O密集型操作</strong>。<code>map</code>
                          函数是顺序执行的。它会先对第一个ID调用RPC，等待其返回，然后再对第二个ID调用RPC，如此往复。总耗时约等于
                          1000 * (单次RPC耗时)。
                        </p>
                      </div>

                      <div class="solutions">
                        <h4>业界主流解决方案与权衡</h4>
                        <div class="solution-demo">
                          <h6>方案：并行化序列操作</h6>
                          <p>
                            将 <code>map</code> 替换为 <code>pmap</code> (<code>parallel map</code
                            >)。
                          </p>
                          <div class="code-example">
                            <pre><code>(let [user-ids (get-platinum-user-ids-from-db)
      user-details (pmap #(rpc-get-user-details %) user-ids)] ; 使用 pmap
  (process-details user-details))</code></pre>
                          </div>
                          <p>
                            <code>pmap</code> 会在一个 Clojure
                            托管的线程池中，并行地对序列中的元素应用函数。
                          </p>
                        </div>

                        <div class="tradeoffs">
                          <h6>利弊与权衡</h6>
                          <div class="tradeoff-grid">
                            <div class="pros">
                              <h7>优点</h7>
                              <ul>
                                <li>
                                  极大地提升了I/O密集型任务的处理速度。总耗时理论上约等于 (1000 /
                                  线程池大小) * (单次RPC耗时)。对于这个场景，效果立竿见影。
                                </li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h7>缺点</h7>
                              <ul>
                                <li>
                                  <code>pmap</code>
                                  适用于<strong>I/O密集型</strong>或每个元素计算都<strong>非常耗时</strong>的场景。如果函数本身只是一个简单的内存计算（如
                                  <code>inc</code>），使用
                                  <code>pmap</code> 反而会因为线程调度的开销而变得更慢。
                                </li>
                                <li>并且，它返回的结果顺序是不确定的。</li>
                              </ul>
                            </div>
                          </div>
                        </div>

                        <div class="industry-choice">
                          <h6>业界选择</h6>
                          <p>
                            在处理一个序列，且序列中每个元素的处理都是一个独立的、耗时的（特别是I/O）操作时，<strong
                              >使用 <code>pmap</code> 是一个简单而高效的并行化标准实践</strong
                            >。但必须清醒地认识到它不适用于CPU密集型的、短小的计算任务。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 元编程利器：宏 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="元编程利器：宏 (Macros: Code that Writes Code)"
                :concept-data="macrosData"
                @interaction="handleInteraction"
              >
                <div class="macros-showcase">
                  <h3>🎭 宏：生成代码的代码</h3>
                  <p class="intro-text">
                    宏是一种特殊的函数，它在<strong>编译期</strong>执行。宏的输入不是值，而是<strong>代码本身</strong>（以
                    Clojure 数据结构的形式）；它的输出也不是值，而是<strong>新的代码</strong>。
                  </p>

                  <div class="concept-explanation">
                    <h4>🎯 人话版解释</h4>
                    <p class="human-explanation">
                      普通函数是"处理数据的代码"，而宏是"<strong>生成代码的代码</strong>"。它就像一个代码模板，你给它一些片段，它在编译的时候就能帮你拼装出一段全新的、更复杂的代码。
                    </p>
                  </div>

                  <div class="core-principles">
                    <h4>🏗️ 深度解读与"第一性原理"追问</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <h6>存在价值</h6>
                        <p>
                          为了让语言本身具备<strong>可扩展性</strong>。当语言自身的语法无法优雅地表达某种逻辑时（例如，新的控制流），你可以通过宏来创造出"仿佛语言原生支持"的新语法。
                        </p>
                      </div>
                      <div class="principle-card">
                        <h6>核心原理</h6>
                        <p>
                          <strong>同像性 (Homoiconicity)</strong>，即"代码即数据"。因为 Clojure
                          的代码本身就是用其核心数据结构（列表）来书写的，所以代码可以像普通数据一样被函数（宏）来处理和转换。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="macro-example">
                    <h4>🔧 具象案例</h4>
                    <div class="demo-section">
                      <h6>Clojure 的 -> (threading-first) 宏</h6>
                      <div class="code-example">
                        <pre><code>; 宏调用
(-> "hello" .toUpperCase (.substring 1))

; 在编译期，这个宏会被展开成：
(.substring (.toUpperCase "hello") 1)</code></pre>
                      </div>
                      <p class="explanation">
                        它把一个嵌套的、从内到外读的函数调用，变成了一个线性的、从上到下读的数据流，极大地增强了可读性。
                      </p>
                    </div>

                    <div class="analogy">
                      <h6>🎨 生动类比</h6>
                      <p>
                        宏就像一个强大的
                        <strong>Excel 宏</strong
                        >。你不是手动地去合并单元格、计算公式，而是录制（或编写）一个宏，这个宏包含了一系列操作指令。以后你只要点击一下按钮，Excel
                        就会自动帮你执行这一整套复杂的操作。
                      </p>
                    </div>
                  </div>

                  <div class="comparison-section">
                    <h4>🔄 横向对比与关联</h4>
                    <div class="comparison-grid">
                      <div class="comparison-item">
                        <h6>vs. C/C++ 宏</h6>
                        <p>
                          C 的宏是简单的文本替换，不感知语法，非常危险。Clojure
                          的宏是语法感知的，操作的是 AST，更加安全和强大。
                        </p>
                      </div>
                      <div class="comparison-item">
                        <h6>vs. Java 注解处理器</h6>
                        <p>
                          注解处理器也可以在编译期生成代码，但它比 Clojure
                          宏要重量级和复杂得多，使用起来也远没有那么灵活和普遍。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="pitfalls-section">
                    <h4>⚠️ 常见理论误区与注意事项</h4>
                    <div class="pitfall-card danger">
                      <h6>天坑</h6>
                      <p>
                        <strong>滥用宏</strong>。这是 Lisp
                        社区持续了几十年的"魔咒"。新手往往会对宏的威力感到兴奋，并试图用它解决所有问题。
                      </p>
                    </div>
                    <div class="pitfall-card">
                      <h6>第一原则</h6>
                      <p>
                        <strong>能用函数解决的问题，就永远不要用宏</strong
                        >。宏是最后的手段，只在需要控制"求值时机"或"创造新语法"时才应使用。
                      </p>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 项目实践踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题描述</h4>
                        <p>
                          开发者想实现一个 <code>unless</code> 控制流，即
                          <code>(unless (condition) (do-something))</code>，当且仅当
                          <code>condition</code> 为 <code>false</code> 时，才执行
                          <code>do-something</code>。他首先尝试用函数来实现：<code
                            >(defn unless [condition body] (if (not condition) body))</code
                          >。结果发现，无论 <code>condition</code> 是真是假，<code>body</code>
                          部分的代码总是被执行了。
                        </p>
                      </div>

                      <div class="root-cause">
                        <h4>问题根源分析</h4>
                        <p>
                          <strong>函数参数的求值策略</strong>。Clojure 和 Java
                          一样，是及早求值（Eager Evaluation）的。在调用
                          <code>unless</code> 函数时，它的所有参数（<code>condition</code> 和
                          <code>body</code
                          >）会<strong>首先被求值</strong>，然后才将求值后的<strong>结果</strong>传入函数体。所以
                          <code>body</code> 在函数被调用前就已经执行了。
                        </p>
                      </div>

                      <div class="solutions">
                        <h4>业界主流解决方案与权衡</h4>
                        <div class="solution-demo">
                          <h6>方案：使用宏</h6>
                          <div class="code-example">
                            <pre><code>(defmacro unless [condition & body]
  `(if (not ~condition)
     (do ~@body)))</code></pre>
                          </div>
                          <div class="explanation-steps">
                            <ol>
                              <li><code>defmacro</code> 定义了一个宏。</li>
                              <li>
                                宏在编译期执行，它接收到的 <code>condition</code> 和
                                <code>body</code>
                                是<strong>未被求值的代码本身</strong>（作为数据）。
                              </li>
                              <li>
                                <code>`</code> (syntax-quote)
                                是一种模板语法，它会原样返回内部的S表达式。
                              </li>
                              <li>
                                <code>~</code> (unquote) 和 <code>~@</code> (splicing-unquote)
                                是在模板中"挖坑"，告诉编译器把宏接收到的参数填到这里。
                              </li>
                              <li>
                                这个宏的最终效果是，在编译期，将
                                <code>(unless some-cond (do-this))</code>
                                这段代码，直接<strong>转换</strong>成了
                                <code>(if (not some-cond) (do (do-this)))</code> 这段代码。<code
                                  >do-this</code
                                >
                                的执行被延迟到了 <code>if</code> 内部，从而解决了问题。
                              </li>
                            </ol>
                          </div>
                        </div>

                        <div class="industry-choice">
                          <h6>业界选择</h6>
                          <p>
                            <strong>创造新的控制流结构是宏最经典、最正当的用途</strong>。Clojure
                            的核心库中，大量的控制流（如 <code>when</code>, <code>cond</code>,
                            <code>-></code> 等）本身就是用宏实现的。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 6: 与 Java 的共生 -->
            <section id="topic-5" class="topic-section" ref="topic5">
              <ExpandableSection
                title="与 Java 的共生 (Symbiosis with Java)"
                :concept-data="javaInteropData"
                @interaction="handleInteraction"
              >
                <div class="java-interop-showcase">
                  <h3>🤝 极强的双向互操作能力</h3>
                  <p class="intro-text">指 Clojure 与 Java 之间极强的、双向的互操作能力。</p>

                  <div class="concept-explanation">
                    <h4>🎯 人话版解释</h4>
                    <p class="human-explanation">
                      在 Clojure 里调用 Java 代码，就像调用一个普通的 Clojure
                      函数一样简单；反之亦然（虽然需要一些包装）。
                    </p>
                  </div>

                  <div class="core-principles">
                    <h4>🏗️ 深度解读与"第一性原理"追问</h4>
                    <div class="principles-grid">
                      <div class="principle-card">
                        <h6>存在价值</h6>
                        <p>
                          为了直接复用 Java 庞大、成熟的生态系统。Clojure
                          不需要自己去写数据库驱动、HTTP客户端、JSON解析库……它可以直接使用 Java
                          生态中那些经过数十年考验的、最高质量的库。
                        </p>
                      </div>
                      <div class="principle-card">
                        <h6>核心原理</h6>
                        <p>
                          Clojure 最终被编译成 JVM 字节码。它的数据结构（如 Vector,
                          Map）都实现了相应的 Java 集合接口（<code>java.util.List</code>,
                          <code>java.util.Map</code>）。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="interop-examples">
                    <h4>🔧 具象案例</h4>
                    <div class="code-example">
                      <pre><code>; 导入 Java 类
(import java.util.Date)
; 创建 Java 对象
(def now (new Date))
; 调用 Java 实例方法
(.getTime now)
; 调用 Java 静态方法
(System/getProperty "java.version")</code></pre>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 项目实践踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题描述</h4>
                        <p>
                          一个 Clojure 项目需要使用一个 Java 库，该库要求传入一个实现了某个复杂 Java
                          接口（比如有十几个方法的 <code>Listener</code> 接口）的实例。Clojure
                          开发者感到很头疼，因为在 Clojure 中定义一个具名类并实现接口比较繁琐。
                        </p>
                      </div>

                      <div class="solutions">
                        <h4>业界主流解决方案与权衡</h4>
                        <div class="solution-demo">
                          <h6>方案：使用 proxy 宏</h6>
                          <p>
                            Clojure 的
                            <code>proxy</code>
                            宏允许你<strong>即时地、匿名地</strong>创建一个实现了任意 Java
                            接口或继承了任意 Java 类的对象。
                          </p>
                          <div class="code-example">
                            <pre><code>(def my-listener
  (proxy [java.awt.event.MouseListener] []
    (mouseClicked [event] (println "Clicked!"))
    ; ... 可以只实现你关心的方法，其他留空 ...
    (mouseEntered [event])
    (mouseExited [event])
    (mousePressed [event])
    (mouseReleased [event])))</code></pre>
                          </div>
                        </div>

                        <div class="tradeoffs">
                          <h6>利弊与权衡</h6>
                          <div class="tradeoff-grid">
                            <div class="pros">
                              <h7>优点</h7>
                              <ul>
                                <li>
                                  极其简洁。相比 Java 中冗长的匿名内部类语法，<code>proxy</code>
                                  可以用极少的代码完成同样的工作。
                                </li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h7>缺点</h7>
                              <ul>
                                <li>
                                  <code>proxy</code>
                                  的性能略低于预先编译好的具名类，因为它涉及更多的动态分派。但在这种回调、监听器的场景下，性能几乎不是问题。
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>

                        <div class="industry-choice">
                          <h6>业界选择</h6>
                          <p>
                            对于需要实现 Java 接口作为回调或监听器的场景，<strong
                              >使用 <code>proxy</code> 是 Clojure 中最惯用、最高效的实践</strong
                            >。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="chapter-summary">
                    <h3>🧠 本章全局回顾与思维导图</h3>

                    <div class="logical-connection">
                      <h4>📚 逻辑串联</h4>
                      <p class="summary-text">
                        第十章带领我们进行了一次彻底的"编程范式"切换。它始于 Lisp
                        家族的简单统一的<strong>语法</strong>和<strong>不可变的数据结构</strong>，这构成了
                        Clojure
                        世界的基石。基于此，我们领略了其<strong>函数式编程</strong>的强大威力，函数和闭包作为一等公民，让代码组合变得灵活。然后，我们见识了其最核心的<strong
                          >序列抽象 (seq)</strong
                        >，它通过惰性求值，为处理各种数据集（甚至是无限的）提供了统一而高效的接口。为了突破函数的局限，Clojure
                        提供了<strong>宏</strong>这一终极武器，赋予了我们扩展语言自身的能力。最后，这一切先进的理念，都通过与
                        <strong>Java 的无缝互操作</strong>，稳稳地扎根在坚实的 JVM
                        平台之上，使其成为一个既激进又务实的强大工具。
                      </p>
                    </div>

                    <div class="mindmap-section">
                      <h4>🗺️ 思维导图</h4>
                      <div class="mindmap-container">
                        <div class="mindmap-center">
                          <div class="center-node">第十章 Clojure</div>
                        </div>
                        <div class="mindmap-branches">
                          <div class="branch-group">
                            <div class="branch-title">核心理念</div>
                            <div class="branch-items">
                              <span class="branch-item">不可变性 (Immutability)</span>
                              <span class="branch-item">REPL驱动开发</span>
                              <span class="branch-item">函数式编程</span>
                            </div>
                          </div>

                          <div class="branch-group">
                            <div class="branch-title">语法与数据结构</div>
                            <div class="branch-items">
                              <span class="branch-item">S-表达式: <code>(func a b)</code></span>
                              <span class="branch-item">List: <code>'(1 2)</code></span>
                              <span class="branch-item">Vector: <code>[1 2]</code></span>
                              <span class="branch-item">Map: <code>{:a 1}</code></span>
                              <span class="branch-item">Keyword: <code>:a</code></span>
                            </div>
                          </div>

                          <div class="branch-group">
                            <div class="branch-title">函数式编程</div>
                            <div class="branch-items">
                              <span class="branch-item">代码即数据</span>
                              <span class="branch-item">高阶函数</span>
                              <span class="branch-item">闭包</span>
                            </div>
                          </div>

                          <div class="branch-group">
                            <div class="branch-title">序列抽象: seq</div>
                            <div class="branch-items">
                              <span class="branch-item">统一接口 (first, rest)</span>
                              <span class="branch-item highlight">惰性求值 (Laziness)</span>
                              <span class="branch-item">并行处理 (pmap)</span>
                            </div>
                          </div>

                          <div class="branch-group">
                            <div class="branch-title">元编程: 宏</div>
                            <div class="branch-items">
                              <span class="branch-item">编译期代码生成</span>
                              <span class="branch-item">控制求值时机</span>
                              <span class="branch-item">创建DSL</span>
                              <span class="branch-item"
                                ><code>defmacro</code>, <code>`</code>, <code>~</code></span
                              >
                            </div>
                          </div>

                          <div class="branch-group">
                            <div class="branch-title">Java互操作</div>
                            <div class="branch-items">
                              <span class="branch-item">调用Java方法/类</span>
                              <span class="branch-item">实现Java接口 (proxy)</span>
                              <span class="branch-item">利用Java生态</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <BackToTopButton />

    <!-- 浮动章节菜单 -->
    <FloatingChapterMenu :chapters="chapterList" current-chapter="10" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import BackToTopButton from '@/components/BackToTopButton.vue'
import FloatingChapterMenu from '@/components/FloatingChapterMenu.vue'

// 响应式数据
const currentTopic = ref(0)
const progress = ref(0)

// 章节列表
const chapterList = [
  { number: 1, title: 'Java基础', path: '/chapter1' },
  { number: 2, title: 'Java高级特性', path: '/chapter2' },
  { number: 3, title: 'Java 17新特性', path: '/chapter3' },
  { number: 4, title: 'Java模块系统', path: '/chapter4' },
  { number: 5, title: 'Java虚拟机', path: '/chapter5' },
  { number: 6, title: 'Java并发编程', path: '/chapter6' },
  { number: 7, title: 'Java性能优化', path: '/chapter7' },
  { number: 8, title: 'JVM替代语言', path: '/chapter8' },
  { number: 9, title: 'Kotlin', path: '/chapter9' },
  { number: 10, title: 'Clojure', path: '/chapter10' },
]

// 课程主题
const courseTopics = [
  {
    title: 'Clojure 简介与核心理念',
    description: '理解不可变性与REPL驱动开发',
  },
  {
    title: '语法与核心数据结构',
    description: 'S-表达式与四大数据结构',
  },
  {
    title: '函数式编程的精髓',
    description: '代码即数据与高阶函数',
  },
  {
    title: '序列抽象 (seq)',
    description: '统一的集合操作接口',
  },
  {
    title: '元编程利器：宏',
    description: '代码生成代码的能力',
  },
  {
    title: '与 Java 的共生',
    description: '无缝互操作与生态利用',
  },
]

// 概念数据
const clojureIntroductionData = {
  definition:
    'Clojure 是一门运行在 JVM 上的、动态类型的、函数式编程语言，以不可变性和 REPL 驱动开发为核心基石。',
  keyPoints: [
    '不可变性：默认情况下所有数据结构都是不可变的',
    'REPL 驱动开发：交互式编程环境是开发流程的中心',
    '持久化数据结构：通过结构共享实现高效的不可变性',
    '动态编译：REPL 中的代码被即时编译成 JVM 字节码',
  ],
  coreValue: '从根本上简化对复杂状态和并发的管理，通过不可变性彻底消除数据竞争问题。',
  commonMistakes: [
    '认为不可变意味着性能低下',
    '混淆值(value)、符号(symbol)和变量(var)的概念',
    '沿用传统的编写-编译-运行模式而不拥抱REPL',
  ],
}

const syntaxDataStructuresData = {
  definition: 'Clojure 使用 S-表达式（前缀表示法）作为基本语法，提供四种核心的不可变数据结构。',
  keyPoints: [
    'S-表达式：(函数名 参数1 参数2 ...) 的统一格式',
    'List ()：不可变链表，适合头部操作',
    'Vector []：不可变数组，支持快速随机访问',
    'Map {}：不可变哈希映射，键值对存储',
    'Keyword :：特殊标识符，常用作Map的key',
  ],
  coreValue: '极致统一的语法使得语言解析和代码生成都变得异常简单，为宏系统奠定基础。',
  commonMistakes: [
    '混淆List和Vector的性能特征',
    '忘记在数据列表前加单引号阻止求值',
    '不理解conj函数对不同数据结构的多态行为',
  ],
}

const functionalProgrammingData = {
  definition: 'Clojure 中代码和数据使用相同的结构，函数是一等公民，支持高阶函数和组合式编程。',
  keyPoints: [
    '代码即数据：程序可以像操作数据一样操作代码',
    '高阶函数：map、filter、reduce等核心函数',
    '函数组合：通过组合简单函数构建复杂逻辑',
    '不可变性：避免副作用，提高代码可预测性',
  ],
  coreValue: '通过函数式编程范式，实现更高的代码组合性、重用性和可维护性。',
  commonMistakes: [
    '误用List作为reduce的累积器',
    '不理解惰性求值的特性',
    '过度使用递归而不使用尾递归优化',
  ],
}

const sequenceAbstractionData = {
  definition: '序列抽象提供统一的接口来操作所有类型的集合，支持惰性求值和无限序列。',
  keyPoints: [
    '统一接口：所有集合类型都可以序列化',
    '惰性求值：元素按需计算，节省内存',
    '无限序列：可以处理理论上无限大的数据集',
    '链式操作：支持优雅的函数式编程风格',
  ],
  coreValue: '通过统一的序列抽象，简化集合操作，提供强大的数据处理能力。',
  commonMistakes: [
    '不理解惰性求值的延迟特性',
    '在需要立即求值的场景下忘记使用doall',
    '对无限序列使用count等会导致无限循环的操作',
  ],
}

const macrosData = {
  definition: '宏是在编译时生成代码的强大工具，让开发者能够扩展语言语法和创建DSL。',
  keyPoints: [
    '编译时代码生成：宏在编译时展开为实际代码',
    '语法扩展：可以创建新的语言结构',
    'DSL构建：构建领域特定语言',
    '零运行时开销：宏展开后无额外性能损失',
  ],
  coreValue: '提供元编程能力，让开发者能够根据需要塑造和扩展语言。',
  commonMistakes: ['过度使用宏而不是函数', '不理解宏的卫生性问题', '在宏中使用副作用操作'],
}

const javaInteropData = {
  definition: 'Clojure与Java的无缝互操作，可以直接调用Java类和方法，充分利用Java生态系统。',
  keyPoints: [
    'Java方法调用：直接调用Java实例和静态方法',
    'Java对象创建：创建和使用Java对象',
    '接口实现：通过reify实现Java接口',
    '类型提示：使用类型提示优化性能',
  ],
  coreValue: '充分利用Java生态系统，实现渐进式迁移和混合开发。',
  commonMistakes: [
    '不使用类型提示导致反射性能损失',
    '未能适应REPL驱动的开发模式',
    '过度依赖Java思维而不拥抱函数式编程',
  ],
}

// 方法
const scrollToTopic = (index: number) => {
  const element = document.getElementById(`topic-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    currentTopic.value = index
  }
}

const handleInteraction = (data: any) => {
  console.log('Interaction:', data)
}

const toggleNotes = () => {
  console.log('Toggle notes')
}

const showQuiz = () => {
  console.log('Show quiz')
}

const updateProgress = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
  progress.value = (scrollTop / scrollHeight) * 100

  // 更新当前主题
  const topics = document.querySelectorAll('.topic-section')
  topics.forEach((topic, index) => {
    const rect = topic.getBoundingClientRect()
    if (rect.top <= 100 && rect.bottom >= 100) {
      currentTopic.value = index
    }
  })
}

onMounted(() => {
  window.addEventListener('scroll', updateProgress)
  updateProgress()
})

onUnmounted(() => {
  window.removeEventListener('scroll', updateProgress)
})
</script>

<style scoped>
.java-chapter10 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0 2rem;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    repeat;
  opacity: 0.1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chapter-info h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chapter-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.chapter-description {
  font-size: 1.1rem;
  opacity: 0.8;
  max-width: 600px;
  line-height: 1.6;
}

.progress-indicator {
  position: relative;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-fill {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 600;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content-wrapper {
  background: white;
  border-radius: 20px 20px 0 0;
  margin-top: 2rem;
  min-height: calc(100vh - 200px);
  box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.1);
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  padding: 2rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: #f8fafc;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
}

.outline h3 {
  margin-bottom: 1rem;
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.outline-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.outline-item:hover {
  background: white;
  border-color: #667eea;
  transform: translateX(4px);
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.outline-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.outline-content p {
  font-size: 0.8rem;
  opacity: 0.7;
  line-height: 1.3;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  padding: 0.75rem;
  border: none;
  border-radius: 12px;
  background: #f1f5f9;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.tool-button:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.main-content {
  padding: 1rem 0;
  overflow-x: hidden;
  max-width: 100%;
}

.topic-section {
  margin-bottom: 3rem;
  scroll-margin-top: 100px;
  overflow-x: hidden;
  max-width: 100%;
}

/* Clojure 特定样式 */
.clojure-introduction-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
  margin-bottom: 2rem;
}

.core-pillars {
  margin: 2rem 0;
}

.pillars-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.pillar-card {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.pillar-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.pillar-card h5 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.pillar-card p {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.analogy {
  background: rgba(102, 126, 234, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.5;
}

.immutability-demo {
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
  border: 1px solid #e2e8f0;
}

.code-example {
  background: #1a202c;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  overflow-x: auto;
  max-width: 100%;
  word-wrap: break-word;
}

.code-example pre {
  margin: 0;
  color: #e2e8f0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

.explanation {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

/* 思维导图样式 */
.clojure-mindmap {
  margin: 3rem 0;
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.mindmap-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.mindmap-center {
  margin-bottom: 1rem;
}

.center-node {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  margin: 0 auto;
}

.mindmap-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  width: 100%;
  max-width: 800px;
}

.concept-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  text-align: center;
}

.concept-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
}

.concept-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.concept-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.concept-header h6 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.concept-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.concept-item {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 8px;
  padding: 0.5rem 0.8rem;
  font-size: 0.9rem;
  color: #4a5568;
  transition: all 0.3s ease;
}

.concept-item:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-1px);
}

.syntax-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.syntax-demo {
  margin: 2rem 0;
}

.syntax-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;
}

.traditional,
.clojure {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.traditional h6,
.clojure h6 {
  margin-bottom: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.syntax-benefits {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.benefit {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.data-structures-grid {
  margin: 3rem 0;
}

.structures-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.structure-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.structure-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.structure-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.structure-symbol {
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  font-family: 'Fira Code', monospace;
}

.structure-header h5 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
}

.structure-content p {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.performance {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.perf-item {
  font-size: 0.8rem;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  background: #f1f5f9;
  color: #475569;
}

/* 函数式编程样式 */
.functional-programming-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.code-as-data-demo {
  margin: 2rem 0;
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.demo-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;
}

.demo-item h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.insight {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.higher-order-functions {
  margin: 3rem 0;
}

.hof-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.example-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.example-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.example-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.problem-solution {
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.problem {
  margin-bottom: 2rem;
}

.problem h4 {
  color: #e53e3e;
  margin-bottom: 1rem;
}

.root-cause {
  margin-bottom: 2rem;
}

.root-cause h4 {
  color: #d69e2e;
  margin-bottom: 1rem;
}

.solutions h4 {
  color: #38a169;
  margin-bottom: 1rem;
}

.solution-demo {
  margin: 1.5rem 0;
}

.performance-comparison {
  margin: 1.5rem 0;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1rem;
}

.comparison-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.comparison-item h7 {
  font-weight: 600;
  color: #2d3748;
  display: block;
  margin-bottom: 0.5rem;
}

.comparison-item ul {
  margin: 0;
  padding-left: 1rem;
}

.comparison-item li {
  font-size: 0.9rem;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.best-practice-note {
  background: rgba(56, 161, 105, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #38a169;
  margin-top: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 序列抽象样式 */
.sequence-abstraction-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.seq-universality {
  margin: 2rem 0;
}

.seq-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.seq-example {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.seq-example h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.lazy-evaluation {
  margin: 3rem 0;
}

.lazy-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.demo-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.demo-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.demo-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

/* 新增通用样式 */
.concept-explanation {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border: 1px solid #e2e8f0;
}

.human-explanation {
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  font-style: italic;
}

.core-principles {
  margin: 2rem 0;
}

.principles-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1rem;
}

.principle-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.principle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.principle-card h6 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.8rem;
}

.principle-card p {
  color: #4a5568;
  line-height: 1.5;
}

.value-points p {
  margin-bottom: 0.8rem;
}

.comparison-section {
  margin: 2rem 0;
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.comparison-item {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.comparison-item h6 {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.comparison-item p {
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

.pitfalls-section {
  margin: 2rem 0;
}

.pitfall-card {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid #f56565;
}

.pitfall-card.danger {
  background: #fffaf0;
  border-color: #fbd38d;
  border-left-color: #ed8936;
}

.pitfall-card h6 {
  font-weight: 600;
  color: #c53030;
  margin-bottom: 0.5rem;
}

.pitfall-card.danger h6 {
  color: #c05621;
}

.pitfall-card p {
  color: #4a5568;
  line-height: 1.4;
  margin: 0;
}

.demo-section {
  margin: 1.5rem 0;
}

.demo-section h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.explanation {
  margin-top: 1rem;
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

.practical-example {
  margin: 2rem 0;
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.tradeoffs {
  margin: 1.5rem 0;
}

.tradeoff-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.pros,
.cons {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.pros h7 {
  color: #38a169;
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}

.cons h7 {
  color: #e53e3e;
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1rem;
}

.pros li,
.cons li {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.industry-choice {
  background: rgba(56, 161, 105, 0.1);
  border: 1px solid rgba(56, 161, 105, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.industry-choice h6 {
  color: #2f855a;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.industry-choice p {
  color: #4a5568;
  line-height: 1.4;
  margin: 0;
}

.explanation-steps {
  margin: 1rem 0;
}

.explanation-steps ol {
  padding-left: 1.2rem;
}

.explanation-steps li {
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* 章节总结样式 */
.chapter-summary {
  margin: 3rem 0;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid #e2e8f0;
}

.logical-connection {
  margin-bottom: 2rem;
}

.summary-text {
  font-size: 1rem;
  line-height: 1.7;
  color: #4a5568;
  text-align: justify;
}

.mindmap-section {
  margin-top: 2rem;
}

.mindmap-branches {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.branch-group {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.branch-group:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.branch-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.branch-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.branch-item {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  padding: 0.5rem 0.8rem;
  font-size: 0.9rem;
  color: #4a5568;
  transition: all 0.3s ease;
  text-align: center;
}

.branch-item:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-1px);
}

.branch-item.highlight {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
  font-weight: 600;
}

.branch-item code {
  background: rgba(255, 255, 255, 0.8);
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.8rem;
}

.branch-item.highlight code {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 宏相关样式 */
.macros-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.macro-concept {
  margin: 2rem 0;
}

.macro-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 150px;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 1rem;
}

.step-content h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

.flow-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

.macro-example {
  margin: 2rem 0;
  background: #f7fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.macro-power {
  margin: 3rem 0;
}

.power-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.power-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: transform 0.3s ease;
}

.power-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.power-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.power-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.power-card p {
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Java 互操作样式 */
.java-interop-showcase {
  padding: 2rem;
  overflow-x: hidden;
  max-width: 100%;
}

.interop-examples {
  margin: 2rem 0;
}

.interop-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.interop-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease;
}

.interop-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.interop-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.ecosystem-benefits {
  margin: 3rem 0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.benefit-card {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: transform 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.benefit-card h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.benefit-card p {
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.4;
}

.solution-steps {
  margin: 1.5rem 0;
}

.step {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
}

.step h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step p {
  color: #4a5568;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.step ul {
  margin: 0.5rem 0 0 1rem;
}

.step li {
  color: #4a5568;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .outline-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .pillars-grid {
    grid-template-columns: 1fr;
  }

  .syntax-comparison {
    grid-template-columns: 1fr;
  }

  .demo-grid {
    grid-template-columns: 1fr;
  }

  .hof-examples {
    grid-template-columns: 1fr;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .lazy-demo {
    grid-template-columns: 1fr;
  }

  .macro-flow {
    flex-direction: column;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .power-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .interop-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .chapter-header {
    padding: 2rem 0 1rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .chapter-info h1 {
    font-size: 2.5rem;
  }

  .container {
    padding: 0 1rem;
  }

  .content-layout {
    padding: 1rem;
  }

  .structures-showcase {
    grid-template-columns: 1fr;
  }

  .seq-examples {
    grid-template-columns: 1fr;
  }

  .power-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .macro-flow {
    gap: 0.5rem;
  }

  .flow-step {
    min-width: 120px;
  }

  .step-number {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .mindmap-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .center-node {
    width: 80px;
    height: 80px;
    font-size: 1rem;
  }

  .concept-card {
    padding: 1rem;
  }

  .concept-icon {
    font-size: 1.5rem;
  }

  .concept-header h6 {
    font-size: 1rem;
  }

  .concept-item {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }
}
</style>
